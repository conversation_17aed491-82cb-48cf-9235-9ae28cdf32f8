<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { PlusOutlined } from '@ant-design/icons-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    disabled?: boolean;
    modelValue?: string;
    placeholder?: string;
    selectedAccountId?: string; // 选中的会计科目ID，用于获取对应的辅助项目
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
    (e: 'addAuxiliary'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    modelValue: '',
    placeholder: '请选择辅助项目',
    selectedAccountId: '',
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const {
    accountSubjects,
    assistantAccounting,
    extractLeafSubjects,
    getAssistantOptions,
    loading,
  } = useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 获取当前选中科目的信息
  const selectedAccount = computed(() => {
    console.log('🔍 AuxiliaryProjectSelector - 查找科目:', {
      accountSubjectsLength: accountSubjects.value?.length || 0,
      hasAccountSubjects: !!accountSubjects.value,
      selectedAccountId: props.selectedAccountId,
    });

    if (!props.selectedAccountId || !accountSubjects.value) return null;

    // 从叶子节点中查找科目
    const leafSubjects = extractLeafSubjects(accountSubjects.value);
    const foundSubject = leafSubjects.find((subject) => {
      // 优先通过ID匹配（现有科目）
      if (
        subject.id &&
        (subject.id === Number(props.selectedAccountId) ||
          subject.id.toString() === props.selectedAccountId)
      ) {
        return true;
      }

      // 如果ID匹配失败，尝试通过code匹配（新增科目）
      if (subject.code === props.selectedAccountId) {
        return true;
      }

      return false;
    });

    console.log('📋 AuxiliaryProjectSelector - 科目查找结果:', {
      found: !!foundSubject,
      foundSubject: foundSubject
        ? {
            assistantType: foundSubject.assistantType,
            code: foundSubject.code,
            id: foundSubject.id,
            name: foundSubject.fullName,
            useAssistant: foundSubject.useAssistant,
          }
        : null,
      selectedAccountId: props.selectedAccountId,
      totalLeafSubjects: leafSubjects.length,
    });

    return foundSubject;
  });

  // 判断是否为新增科目：通过 account_code 是否为 "404" 来判断
  const isNewSubject = computed(() => {
    console.log('selectedAccount1111', selectedAccount);

    if (!selectedAccount.value) return false;
    const account = selectedAccount.value as any;
    console.log('account.code222', account.code);
    // 主要判断条件：通过 account_code 是否为 "404" 来判断新增科目
    return account.code === '404';
  });

  // 判断是否开启了辅助核算
  const isAuxiliaryEnabled = computed(() => {
    console.log('🔍 AuxiliaryProjectSelector - 判断辅助核算状态:', {
      assistantType: selectedAccount.value?.assistantType,
      isNewSubject: isNewSubject.value,
      selectedAccount: selectedAccount.value,
      selectedAccountId: props.selectedAccountId,
      useAssistant: selectedAccount.value?.useAssistant,
    });

    // 如果是新增科目，默认开启辅助核算
    if (isNewSubject.value) {
      console.log('✅ 识别为新增科目，开启辅助核算');
      return true;
    }

    // 如果没有选中科目，返回false
    if (!selectedAccount.value) {
      console.log('⚠️ 未找到选中科目，辅助核算状态为false');
      return false;
    }

    const enabled =
      selectedAccount.value.useAssistant === true ||
      selectedAccount.value.assistantType;

    console.log('📋 现有科目辅助核算状态:', enabled);
    return enabled;
  });

  // 获取辅助项目选项
  const auxiliaryOptions = computed(() => {
    console.log('🎯 AuxiliaryProjectSelector - 计算辅助项目选项:', {
      currentValue: props.modelValue,
      hasSelectedAccount: !!selectedAccount.value,
      isAuxiliaryEnabled: isAuxiliaryEnabled.value,
      selectedAccount: selectedAccount.value
        ? {
            assistantType: selectedAccount.value.assistantType,
            id: selectedAccount.value.id,
            useAssistant: selectedAccount.value.useAssistant,
          }
        : null,
    });

    if (!isAuxiliaryEnabled.value || !selectedAccount.value) {
      console.log('⚠️ 辅助核算未启用或未选择科目，返回空选项');
      return [];
    }

    // 获取该科目的辅助核算选项
    const assistantType = selectedAccount.value?.assistantType;

    console.log('🔧 获取辅助核算选项:', {
      assistantAccountingKeys: assistantAccounting.value
        ? Object.keys(assistantAccounting.value)
        : [],
      assistantType,
      hasAssistantAccounting: !!assistantAccounting.value,
      isNewSubject: isNewSubject.value,
    });

    if (assistantAccounting.value) {
      let options: any[] = [];

      if (isNewSubject.value) {
        // 新增科目可以选择所有类型的辅助核算
        for (const [type] of Object.entries(assistantAccounting.value)) {
          const typeOptions = getAssistantOptions(type);
          options.push(...typeOptions);
        }
      } else if (assistantType) {
        // 现有科目只能选择对应类型的辅助核算
        options = getAssistantOptions(assistantType);
      }

      console.log('📊 获取到的辅助核算选项:', {
        assistantType,
        isNewSubject: isNewSubject.value,
        options: options.slice(0, 3), // 只显示前3个，避免日志过长
        optionsCount: options.length,
      });

      // 适配 antd select 的 options 格式
      const mappedOptions = options.map((item) => ({
        ...item,
        label: `${item.code} ${item.name}`,
        // 新增辅助项目的ID为null，使用code作为value；现有辅助项目使用ID
        value: item.id ? item.id.toString() : item.code,
      }));

      // 添加"无辅助项目"选项
      const finalOptions = [
        { code: '', label: '无辅助项目', name: '无辅助项目', value: '' },
        ...mappedOptions,
      ];

      // 检查当前值是否在选项中，如果不在则添加为默认选项
      if (props.modelValue && props.modelValue !== '') {
        const currentValueExists = finalOptions.some(
          (option) => option.value === props.modelValue,
        );

        if (!currentValueExists) {
          console.log(
            '⚠️ 当前辅助项目值在选项中不存在，添加为默认选项:',
            props.modelValue,
          );

          // 尝试从原始辅助核算数据中查找匹配的项目
          let foundAuxiliaryItem = null;

          // 遍历所有类型的辅助核算数据查找匹配项
          if (assistantAccounting.value) {
            for (const [, items] of Object.entries(assistantAccounting.value)) {
              const found = items.find(
                (item) =>
                  item.id?.toString() === props.modelValue ||
                  item.code === props.modelValue,
              );
              if (found) {
                foundAuxiliaryItem = found;
                break;
              }
            }
          }

          if (foundAuxiliaryItem) {
            // 如果找到了匹配的辅助项目，使用其完整信息
            finalOptions.push({
              code: foundAuxiliaryItem.code,
              label: `${foundAuxiliaryItem.code} ${foundAuxiliaryItem.name}`,
              name: foundAuxiliaryItem.name,
              value: foundAuxiliaryItem.id
                ? foundAuxiliaryItem.id.toString()
                : foundAuxiliaryItem.code,
            });
          } else {
            // 如果没找到，尝试解析当前值
            let displayLabel = props.modelValue;
            let codeValue = props.modelValue;
            let nameValue = props.modelValue;

            // 如果当前值包含空格，尝试拆分为code和name
            if (props.modelValue.includes(' ')) {
              const parts = props.modelValue.split(' ');
              if (parts.length >= 2) {
                codeValue = parts[0] || props.modelValue;
                nameValue = parts.slice(1).join(' ') || props.modelValue;
                displayLabel = props.modelValue; // 保持原始格式显示
              }
            }

            // 添加当前值作为默认选项，显示原始内容
            finalOptions.push({
              code: codeValue,
              label: displayLabel, // 显示完整的code name格式
              name: nameValue,
              value: props.modelValue,
            });
          }
        }
      }

      console.log('✅ 最终辅助项目选项:', {
        currentValueInOptions: props.modelValue
          ? finalOptions.some((opt) => opt.value === props.modelValue)
          : false,
        firstFewOptions: finalOptions
          .slice(0, 3)
          .map((opt) => ({ label: opt.label, value: opt.value })),
        hasNoAuxiliaryOption: finalOptions[0]?.label === '无辅助项目',
        totalCount: finalOptions.length,
      });

      return finalOptions;
    }

    console.log('⚠️ 无辅助核算类型或数据，返回默认选项');
    return [{ code: '', label: '无辅助项目', name: '无辅助项目', value: '' }];
  });

  // 显示的占位符文本
  const displayPlaceholder = computed(() => {
    if (!props.selectedAccountId) {
      return '请先选择会计科目';
    }
    if (!isAuxiliaryEnabled.value) {
      return '未开启辅助核算';
    }
    return props.placeholder;
  });

  // 是否禁用选择框
  const isDisabled = computed(() => {
    return (
      props.disabled || !props.selectedAccountId || !isAuxiliaryEnabled.value
    );
  });

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      (option.code && option.code.toLowerCase().includes(searchText)) ||
      (option.name && option.name.toLowerCase().includes(searchText))
    );
  };

  // 选择变化处理
  const handleChange = (value: any, option?: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);

    if (option) {
      emit('change', stringValue, option);
    }
  };

  // 新增辅助项目
  const handleAddAuxiliary = () => {
    emit('addAuxiliary');
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
    { immediate: true },
  );

  // 使用防抖来处理科目变化，避免在快速连续变化时误清空
  let clearTimer: NodeJS.Timeout | null = null;

  // 监听选中科目变化，清空辅助项目选择
  watch(
    () => props.selectedAccountId,
    (newAccountId, oldAccountId) => {
      // 清除之前的定时器
      if (clearTimer) {
        clearTimeout(clearTimer);
        clearTimer = null;
      }

      // 只有在科目真正发生变化且不是初始设置时才考虑清空辅助项目
      if (oldAccountId !== undefined && newAccountId !== oldAccountId) {
        console.log('🔄 科目变化，延迟判断是否清空辅助项目选择:', {
          currentModelValue: props.modelValue,
          newAccountId,
          oldAccountId,
        });

        // 延迟执行清空操作，给 modelValue 更新留出时间
        clearTimer = setTimeout(() => {
          // 如果此时 modelValue 为空，说明确实需要清空
          // 如果 modelValue 有值，说明是编辑现有数据，不应该清空
          if (props.modelValue) {
            console.log('🔄 保留辅助项目选择，因为有现有值:', props.modelValue);
          } else {
            console.log('🔄 执行清空辅助项目选择');
            selectedValue.value = '';
            emit('update:modelValue', '');
          }
          clearTimer = null;
        }, 50); // 50ms 延迟，确保 modelValue 有时间更新
      }
    },
  );
</script>

<template>
  <div class="auxiliary-project-selector">
    <a-input
      v-if="!isAuxiliaryEnabled && props.selectedAccountId"
      disabled
      style="width: 100%"
      value="未开启辅助核算"
    />
    <a-select
      v-else
      v-model:value="selectedValue"
      :placeholder="displayPlaceholder"
      show-search
      allow-clear
      :disabled="isDisabled"
      :loading="loading"
      :options="auxiliaryOptions"
      :filter-option="filterOption"
      style="width: 100%"
      @change="handleChange"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          加载中...
        </div>
        <div v-else-if="!props.selectedAccountId">请先选择会计科目</div>
        <div v-else>暂无数据</div>
      </template>

      <!-- 下拉框底部的新增辅助项目按钮 -->
      <template #dropdownRender="{ menuNode }">
        <div v-if="isAuxiliaryEnabled && props.selectedAccountId">
          <component :is="menuNode" />
          <a-divider style="margin: 4px 0" />
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 4px 8px;
              color: #1890ff;
              cursor: pointer;
            "
            @click="handleAddAuxiliary"
          >
            <PlusOutlined style="margin-right: 4px" />
            新增辅助项目
          </div>
        </div>
        <div v-else>
          <component :is="menuNode" />
        </div>
      </template>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
  .auxiliary-project-selector {
    width: 100%;
  }
</style>
